<NavBar nav-data="{{ navData }}"/>
<view class="container" style="margin-top: {{ paddingHeight }}px" catchtouchmove="preventPageScroll">
<!--  <button class="test-btn" style="top: 200rpx; left: 0" size="mini" bindtap="resetTutorial">-->
<!--    重置教程-->
<!--  </button>-->
  <!-- 剪辑状态 头部区域 -->
  <view class="header-box {{isClipping ? 'show' : ''}}">
    <view class="header-box-left">
      <t-icon name="close" size="60rpx" bindtap="closeClip"></t-icon>
      <t-icon name="search" size="55rpx" class="not-online-icon" bindtap="notOnlineFunction"></t-icon>
    </view>
    <view class="header-box-right">
      <!--      <view class="save-slice-btn {{isRecording ? 'activate' : ''}}" bindtap="confirmRecord">保存切片</view>-->
      <view>当前剩余权益：{{userRights.remainFormatted}}</view>
    </view>
  </view>
  <!-- 视频播放区域 -->
  <view class="video-box {{isClipping ? 'recording' : ''}}">
    <!-- src="{{cameraList[activateCameraIndex]['liveUrl']}}"-->
    <view class="video-box-player">
      <video
        id="myVideo"
        src="{{ videoUrl }}"
        custom-cache="{{false}}"
        show-center-play-btn="{{ false }}"
        show-fullscreen-btn
        show-play-btn="{{ false }}"
        show-progress="{{ false }}"
        controls
        show-bottom-progress="{{ false }}"
        vslide-gesture-in-fullscreen
        enable-play-gesture="{{true}}"
        vslide-gesture="{{true}}"
        show-snapshot-button="{{true}}"
        muted="{{ isMuted }}"
        bindloadeddata="onVideoLoadedData"
        bindcanplay="onVideoCanPlay"
        bindplay="onVideoPlay"
        bindpause="onVideoPause"
        binderror="onVideoError"
        enable-auto-rotation="{{false}}"
        is-live="{{true}}"
      ></video>

      <!-- 静音按钮 - 动态定位到视频右上角 -->
      <view class="mute-toggle {{isMuted ? 'mute-toggle--active' : ''}}" bindtap="changeMute">
        <image class="mute-toggle__icon" src="/images/camera/muted.png"/>
        <text class="mute-toggle__text">静音</text>
      </view>

      <!--      &lt;!&ndash; 刷新按钮 - 动态定位到视频右下角 &ndash;&gt;-->
      <!--      <view class="refresh-toggle" bindtap="refreshVideo">-->
      <!--        <t-icon name="refresh" size="40rpx" bindtap="refreshVideo"/>-->
      <!--      </view>-->
    </view>
  </view>
  <!-- 控制按钮区域 -->
  <view class="control-buttons" wx:if="{{isClipping}}">
    <!--    <view class="control-buttons-left">播放时间：{{formattedCurrentTime}}</view>-->
    <view class="control-buttons-left">
      <t-icon name="close-octagon" size="45rpx"
              style="color: {{isRecording ? 'white':'#595959'}};margin-right: 30rpx;"
              bindtap="cancelRecordArea"></t-icon>
      <t-icon style="color:{{isRecording ? '#595959':'white'}}" name="refresh" size="40rpx" bindtap="refreshVideo"/>
    </view>
    <view class="control-buttons-center">
      <!-- 回到最左侧时间 -->
      <t-icon name="backward" size="{{iconSize}}" bindtap="jumpToStartTime"/>
      <!-- 播放/暂停图标 -->
      <t-icon name="{{isPlaying ? 'pause' : 'play'}}" size="70rpx" bindtap="togglePlay"/>
      <!-- 点击回到直播 -->
      <t-icon name="forward" size="{{iconSize}}" bindtap="jumpToLatestTime"/>
    </view>
    <view class="control-buttons-right">
      <t-icon style="margin-right: 30rpx; color: {{canZoomOut && !isRecording ? 'white':'#595959'}}"
              name="zoom-out"
              size="{{iconSize}}"
              bindtap="zoomOut"/>
      <t-icon style="color: {{canZoomIn && !isRecording ? 'white':'#595959'}}"
              name="zoom-in"
              size="{{iconSize}}"
              bindtap="zoomIn"/>
    </view>
  </view>
  <!-- 时间轨道容器 - 使用原生slider重构 -->
  <view class="slider-track-container" wx:if="{{isClipping}}"
        bindtouchstart="handleTouchStart"
        bindtouchmove="handleTouchMove"
        bindtouchend="handleTouchEnd"
        bindtouchcancel="handleTouchEnd">
    <!-- 基准线 - 固定在中央 -->
    <view class="baseline-line"></view>

    <!-- 时间刻度显示层 -->
    <view class="time-scales-layer">
      <view wx:for="{{timeScales}}"
            wx:key="time"
            class="scale-item scale-{{item.scaleType}} {{item.isFuture ? 'scale-future' : ''}}"
            style="left: {{item.position}}px">
        <!-- 时间标签（仅在整分钟显示，在刻度上方） -->
        <text wx:if="{{item.timeText}}"
              class="time-label {{item.isFuture ? 'time-label-future' : ''}}">{{item.timeText}}</text>
        <!-- 刻度线 -->
        <view class="scale-line"></view>
      </view>
    </view>

    <!-- 录制区域显示层 -->
    <view wx:if="{{isRecording}}" class="record-area-layer">
      <view class="record-area {{recordAreaAnimation}}" style="{{recordAreaStyle}}">
        <!-- 左侧拖拽手柄 -->
        <view class="record-drag-handle record-drag-handle-left"
              data-type="start"
              catchtouchstart="handleRecordAreaDragStart"
              catchtouchmove="handleRecordAreaDragMove"
              catchtouchend="handleRecordAreaDragEnd"
              catchtouchcancel="handleRecordAreaDragEnd">
          <view class="drag-handle-tab {{isRecordAreaDragging && recordDragType === 'start' ? 'dragging' : ''}}">
          </view>
        </view>

        <!-- 右侧拖拽手柄 -->
        <view class="record-drag-handle record-drag-handle-right"
              data-type="end"
              catchtouchstart="handleRecordAreaDragStart"
              catchtouchmove="handleRecordAreaDragMove"
              catchtouchend="handleRecordAreaDragEnd"
              catchtouchcancel="handleRecordAreaDragEnd">
          <view class="drag-handle-tab {{isRecordAreaDragging && recordDragType === 'end' ? 'dragging' : ''}}">
          </view>
        </view>

        <!-- 录制区域中心提示 -->
        <view class="record-area-label">录制区域</view>
      </view>
    </view>

    <!-- 原生滑块组件 - 主要交互层 -->
    <view class="slider-layer">
      <slider
        min="{{sliderMin}}"
        max="{{sliderMax}}"
        step="{{sliderStep}}"
        value="{{sliderValue}}"
        backgroundColor="transparent"
        activeColor="transparent"
        block-color="transparent"
        block-size="12"
        bindchange="onSliderChange"
        bindchanging="onSliderChanging"
        class="time-slider"
      />
    </view>
  </view>
  <!-- 切换摄像头区域 -->
  <view class="camera-box" wx:else>
    <view wx:for="{{ cameraList }}"
          wx:key="id"
          data-id="{{ item.id }}"
          data-index="{{ index }}"
          bindtap="switchCamera"
          class="camera-item {{ activateCameraIndex === index ? 'camera-item--active' : '' }}">
      <text class="camera-item__name">{{ item.deviceName }}</text>
    </view>
  </view>
  <!--  进入剪辑状态  -->
  <view class="pre-clip-edit" wx:if="{{!isClipping}}" bindtap="startClip">查看回放/剪辑</view>
  <!--  剪辑状态 底部区域 -->
  <view class="footer-box {{isClipping ? 'footer-box-show' : ''}}">
    <view class="footer-icon-item not-online-icon" bindtap="notOnlineFunction">
      <t-icon name="music-2" size="{{iconSize}}"/>
      <text>音频</text>
    </view>
    <view class="footer-icon-item not-online-icon" bindtap="notOnlineFunction">
      <t-icon name="letters-t" size="{{iconSize}}"/>
      <text>文本</text>
    </view>
    <view class="footer-icon-item not-online-icon" bindtap="notOnlineFunction">
      <t-icon name="logo-chrome" size="{{iconSize}}"/>
      <text>贴纸</text>
    </view>
    <view class="footer-icon-item not-online-icon" bindtap="notOnlineFunction">
      <t-icon name="logo-adobe-illustrate" size="{{iconSize}}"/>
      <text>AI</text>
    </view>
    <view class="footer-icon-item not-online-icon" bindtap="notOnlineFunction">
      <t-icon name="filter-2" size="{{iconSize}}"/>
      <text>特效</text>
    </view>
    <view class="footer-icon-item cut-icon {{isRecording ? 'cut-icon--save-mode' : ''}}"
          bindtap="{{isRecording ? 'confirmRecord' : 'toggleRecord'}}">
      <t-icon name="{{isRecording ? 'check' : 'cut-1'}}" size="{{iconSize}}"/>
      <text>{{isRecording ? '保存' : '剪辑'}}</text>
    </view>
  </view>
  <!-- 切片列表区域 -->
  <view class="slice-video-box {{_isLoadingNewVideoList ? 'slice-video-box--loading' : ''}}" wx:if="{{!isClipping}}">
    <!-- 加载状态提示 -->
    <view class="video-list-loading" wx:if="{{_isLoadingNewVideoList && videoList.length === 0}}">
      <view class="loading-text">正在加载切片列表...</view>
    </view>

    <scroll-view
      class="video-list-scroll"
      scroll-y="{{true}}"
      enhanced="{{true}}"
      show-scrollbar="{{false}}"
      enable-passive="{{true}}">
      <view class="video-item-wrapper {{item.id === swipeDeleteItemId ? 'video-item-wrapper--show-delete' : ''}}"
            wx:for="{{videoList}}"
            wx:key="id">
      <view
        class="video-item {{item.statusChangeType ? 'video-item--status-' + item.statusChangeType : ''}} {{item.sliceStatue !== 20 ? 'video-item--disabled' : ''}}"
        data-url="{{item.objectKey}}"
        data-id="{{item.id}}"
        bindtouchstart="onVideoItemTouchStart"
        bindtouchmove="onVideoItemTouchMove"
        bindtouchend="onVideoItemTouchEnd">
        <view class="video-item__thumbnail {{item._thumbnailLoaded ? 'loaded' : ''}}">
          <view class="devName">机位:{{item.devId}}</view>
          <image
            class="video-item__image {{item._thumbnailLoaded ? 'loaded' : ''}}"
            src="{{item.thumbnail}}"
            data-id="{{item.id}}"
            data-index="{{index}}"
            data-update-key="{{item._imageUpdateKey}}"
            bindload="onThumbnailLoad"
            binderror="onThumbnailError"
          ></image>
        </view>
        <view class="video-item__content">
          <view class="video-item__header">
            <view class="video-item__title">{{item.name}}</view>
            <view class="video-item__duration">
              <image class="video-item__duration-icon" src="/images/live/camera.png" mode="aspectFit"></image>
              <text class="video-item__duration-text">{{item.duration}}</text>
            </view>
          </view>
          <view class="video-item__time-range">
            {{item.startTimeDisplay}} ~ {{item.endTimeDisplay}}
          </view>
        </view>
        <!-- 失败状态遮罩层 -->
        <view
          wx:if="{{item.sliceStatue === 40}}"
          class="video-item__failure-overlay"
          data-id="{{item.id}}"
          data-start-time="{{item.startTime}}"
          data-end-time="{{item.endTime}}"
          catchtap="retrySlicingTask">
          <view class="video-item__retry-btn">
            <text class="video-item__retry-text">点击重试</text>
          </view>
        </view>
        <!-- 录制中状态遮罩层 -->
        <view
          wx:if="{{item.sliceStatue === 1 || item.sliceStatue === 5 || item.sliceStatue === 10}}"
          class="video-item__processing-overlay">
          <view class="video-item__processing-content">
            <text class="video-item__processing-text">切片中...</text>
          </view>
        </view>
        <!-- 切片成功状态遮罩层 -->
        <view
          wx:if="{{item.statusChangeType === 'success'}}"
          class="video-item__success-overlay">
          <view class="video-item__success-content">
            <text class="video-item__success-text">切片成功！</text>
          </view>
        </view>
      </view>
      <view class="video-item__delete-btn"
            data-id="{{item.id}}"
            bindtap="onDeleteVideoItem"
            catchtap="onDeleteVideoItem">
        <t-icon name="delete-1" size="48rpx" data-name="delete-1"/>
      </view>
    </view>
    </scroll-view>
  </view>
  <!-- 终止录制确认对话框 -->
  <view wx:if="{{showStopRecordDialog}}" class="dialog-overlay">
    <view class="dialog-container">
      <view class="dialog-title">确认终止录制</view>
      <view class="dialog-content">
        <text>确定要终止当前录制吗？</text>
        <view class="record-info">
          <text class="record-info-text">录制时长：{{recordDurationText}}</text>
        </view>
      </view>
      <view class="dialog-buttons">
        <button class="dialog-btn dialog-btn-cancel" bindtap="cancelStopRecord">取消</button>
        <button class="dialog-btn dialog-btn-confirm" bindtap="confirmStopRecord">确定</button>
      </view>
    </view>
  </view>
  <!-- 新手教程遮罩层 -->
  <view wx:if="{{showTutorial}}" class="tutorial-overlay">
    <!-- 遮罩背景 -->
    <view class="tutorial-mask"></view>
    <!-- 高亮区域 -->
    <view wx:if="{{tutorialHighlightReady}}"
          class="tutorial-highlight {{tutorialHighlightStyle ? '' : 'tutorial-highlight-' + tutorialData[tutorialStep].target}}"
          style="{{tutorialHighlightStyle ? 'left:' + tutorialHighlightStyle.left + ';top:' + tutorialHighlightStyle.top + ';width:' + tutorialHighlightStyle.width + ';height:' + tutorialHighlightStyle.height + ';z-index:' + tutorialHighlightStyle.zIndex : ''}}"
          bindtap="handleTutorialHighlightTap"
          data-target="{{tutorialData[tutorialStep].target}}"></view>
    <!-- 教程提示框容器 -->
    <view class="tutorial-popup-container tutorial-popup-container-{{tutorialData[tutorialStep].position}}">
      <view class="tutorial-popup">
        <view class="tutorial-header">
          <view class="tutorial-title">{{tutorialData[tutorialStep].title}}</view>
          <view class="tutorial-step">{{tutorialStep}}/3</view>
        </view>
        <view class="tutorial-content">{{tutorialData[tutorialStep].content}}</view>
        <view class="tutorial-buttons">
          <button class="tutorial-btn tutorial-btn-skip" bindtap="skipTutorial">跳过教程</button>
          <button class="tutorial-btn tutorial-btn-next" bindtap="nextTutorialStep">
            {{tutorialStep < 3 ? '下一步' : '完成'}}
          </button>
        </view>
      </view>
    </view>
  </view>
</view>
